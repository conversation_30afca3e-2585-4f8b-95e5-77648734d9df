import React from 'react';
import { But<PERSON>, Box, Typography } from '@mui/material';
import authService from '../services/authService';

const AuthDebug = () => {
  const handleClearAuth = () => {
    console.log('🔧 Debug: Clearing all authentication data');
    authService.clearAllAuthData();
    alert('Authentication data cleared! Refresh the page to see the effect.');
  };

  const handleCheckAuth = () => {
    const isAuth = authService.isAuthenticated();
    const user = authService.getUser();
    const tokens = {
      access: !!authService.getAccessToken(),
      refresh: !!authService.getRefreshToken(),
      id: !!authService.getIdToken()
    };
    
    console.log('🔧 Debug: Current Authentication Status:', {
      isAuthenticated: isAuth,
      user: user,
      hasTokens: tokens
    });
    
    alert(`Auth Status: ${isAuth ? 'Authenticated' : 'Not Authenticated'}\nCheck console for details.`);
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Box 
      sx={{ 
        position: 'fixed', 
        bottom: 16, 
        right: 16, 
        zIndex: 9999,
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
        p: 2,
        bgcolor: 'background.paper',
        border: '1px solid #ccc',
        borderRadius: 1,
        boxShadow: 2
      }}
    >
      <Typography variant="caption" fontWeight="bold">
        🔧 Auth Debug
      </Typography>
      <Button 
        size="small" 
        variant="outlined" 
        color="warning"
        onClick={handleClearAuth}
      >
        Clear Auth
      </Button>
      <Button 
        size="small" 
        variant="outlined" 
        color="info"
        onClick={handleCheckAuth}
      >
        Check Status
      </Button>
    </Box>
  );
};

export default AuthDebug;
