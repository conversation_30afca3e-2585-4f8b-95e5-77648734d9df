import { useEffect } from 'react';

// material-ui
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';

// project imports
import AnimateButton from 'ui-component/extended/AnimateButton';

// ===========================|| KEYCLOAK - REGISTER ||=========================== //

export default function AuthRegister() {
  // Handle Keycloak registration redirect
  const handleKeycloakRegister = () => {
    const keycloakUrl = process.env.REACT_APP_KEYCLOAK_URL || 'https://keycloak-prod.1squalq6nmfj.eu-de.codeengine.appdomain.cloud';
    const realm = process.env.REACT_APP_KEYCLOAK_REALM || 'jiheneline';
    const clientId = process.env.REACT_APP_KEYCLOAK_CLIENT_ID || 'backoffice-client';

    const registrationUrl = `${keycloakUrl}/realms/${realm}/protocol/openid-connect/registrations`;
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: window.location.origin + '/auth/callback',
      response_type: 'code',
      scope: 'openid profile email'
    });

    window.location.href = `${registrationUrl}?${params.toString()}`;
  };

  // Automatically redirect to Keycloak registration on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      handleKeycloakRegister();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '300px',
        textAlign: 'center',
        gap: 3
      }}
    >
      {/* Loading State */}
      <CircularProgress size={40} />

      <Typography variant="h6" gutterBottom>
        Redirecting to Keycloak Registration...
      </Typography>

      <Typography variant="body2" color="textSecondary">
        You will be redirected to the secure registration page
      </Typography>

      {/* Manual redirect button as fallback */}
      <Box sx={{ mt: 2 }}>
        <AnimateButton>
          <Button fullWidth size="large" variant="contained" color="primary" onClick={handleKeycloakRegister}>
            Continue to Registration
          </Button>
        </AnimateButton>
      </Box>

      <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
        Note: This is a backoffice application. New accounts require administrator approval.
      </Typography>

      {/* Fallback message if redirect takes too long */}
      <Typography variant="caption" color="textSecondary" sx={{ mt: 1 }}>
        If you are not redirected automatically, please click the button above
      </Typography>
    </Box>
  );
}
