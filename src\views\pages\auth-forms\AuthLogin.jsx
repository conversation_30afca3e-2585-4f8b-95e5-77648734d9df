import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// material-ui
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';

// project imports
import { redirectToKeycloakLogin } from '../../../utils/keycloak';
import authService from '../../../services/authService';

// ===============================|| JWT - LOGIN ||=============================== //

export default function AuthLogin() {
  const location = useLocation();
  const navigate = useNavigate();

  // Automatically redirect to Keycloak on component mount
  useEffect(() => {
    const initiateKeycloakLogin = async () => {
      try {
        console.log('🔐 AuthLogin component mounted - redirecting to Keycloak');

        // Check if user is already authenticated
        const isAuth = authService.isAuthenticated();
        console.log('🔐 Current auth status:', isAuth);

        if (isAuth) {
          console.log('🔐 User already authenticated, redirecting to dashboard...');
          // If user is already authenticated, redirect to dashboard instead of clearing data
          const returnUrl = location.state?.from || '/app/dashboard/default';
          console.log('🔐 Redirecting authenticated user to:', returnUrl);
          navigate(returnUrl, { replace: true });
          return;
        }

        // Store the intended destination
        const from = location.state?.from || '/app/dashboard/default';
        sessionStorage.setItem('auth_redirect_url', from);

        console.log('🔐 Stored redirect URL:', from);
        console.log('🔐 Redirecting to Keycloak...');

        // Small delay to show loading state, then redirect
        setTimeout(() => {
          redirectToKeycloakLogin();
        }, 500);
      } catch (error) {
        console.error('🔐 Keycloak redirect failed:', error);
        // If redirect fails, show error and retry after delay
        setTimeout(() => {
          initiateKeycloakLogin();
        }, 2000);
      }
    };

    initiateKeycloakLogin();
  }, [location.state?.from, navigate]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '200px',
        textAlign: 'center'
      }}
    >
      {/* Loading State */}
      <CircularProgress size={40} sx={{ mb: 2 }} />

      <Typography variant="h6" gutterBottom>
        Redirecting to Keycloak...
      </Typography>

      <Typography variant="body2" color="textSecondary">
        You will be redirected to the secure login page
      </Typography>

      {/* Fallback message if redirect takes too long */}
      <Typography variant="caption" color="textSecondary" sx={{ mt: 2 }}>
        If you are not redirected automatically, please check your browser settings
      </Typography>
    </Box>
  );
}
