import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// material-ui
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Button from '@mui/material/Button';

// project imports
import { redirectToKeycloakLogin } from '../../../utils/keycloak';
import { useAuth } from '../../../contexts/AuthContext';

// ===============================|| KEYCLOAK - LOGIN ||=============================== //

export default function AuthLogin() {
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();
  const [redirectAttempted, setRedirectAttempted] = useState(false);
  const [showFallbackButton, setShowFallbackButton] = useState(false);

  // Automatically redirect to Keycloak on component mount
  useEffect(() => {
    const initiateKeycloakLogin = async () => {
      try {
        console.log('🔐 AuthLogin component mounted');
        console.log('🔐 Auth loading:', isLoading);
        console.log('🔐 Is authenticated:', isAuthenticated);
        console.log('🔐 Redirect attempted:', redirectAttempted);

        // Wait for auth context to finish loading
        if (isLoading) {
          console.log('🔐 Auth context still loading, waiting...');
          return;
        }

        // If user is already authenticated, redirect to dashboard
        if (isAuthenticated) {
          console.log('🔐 User already authenticated, redirecting to dashboard...');
          const returnUrl = location.state?.from || '/app/dashboard/default';
          console.log('🔐 Redirecting authenticated user to:', returnUrl);
          navigate(returnUrl, { replace: true });
          return;
        }

        // Only attempt redirect once to prevent loops
        if (redirectAttempted) {
          console.log('🔐 Redirect already attempted, not retrying to prevent loop');
          return;
        }

        // Store the intended destination
        const from = location.state?.from || '/app/dashboard/default';
        sessionStorage.setItem('auth_redirect_url', from);

        console.log('🔐 Stored redirect URL:', from);
        console.log('🔐 Redirecting to Keycloak...');

        // Mark that we've attempted the redirect
        setRedirectAttempted(true);

        // Small delay to show loading state, then redirect
        setTimeout(() => {
          redirectToKeycloakLogin();
        }, 1000);

        // Show fallback button after 5 seconds in case redirect fails
        setTimeout(() => {
          setShowFallbackButton(true);
        }, 5000);
      } catch (error) {
        console.error('🔐 Keycloak redirect failed:', error);
        // Don't retry automatically to prevent loops
        setRedirectAttempted(true);
      }
    };

    initiateKeycloakLogin();
  }, [isAuthenticated, isLoading, location.state?.from, navigate, redirectAttempted]);

  // Manual redirect function for fallback button
  const handleManualRedirect = () => {
    console.log('🔐 Manual redirect to Keycloak triggered');
    try {
      redirectToKeycloakLogin();
    } catch (error) {
      console.error('🔐 Manual redirect failed:', error);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '200px',
        textAlign: 'center'
      }}
    >
      {/* Loading State */}
      <CircularProgress size={40} sx={{ mb: 2 }} />

      <Typography variant="h6" gutterBottom>
        {isLoading ? 'Loading...' : redirectAttempted ? 'Redirecting to Keycloak...' : 'Preparing login...'}
      </Typography>

      <Typography variant="body2" color="textSecondary">
        {isLoading
          ? 'Checking authentication status...'
          : redirectAttempted
            ? 'You will be redirected to the secure login page'
            : 'Please wait while we prepare your login'}
      </Typography>

      {/* Fallback button if redirect takes too long */}
      {showFallbackButton && (
        <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
          <Typography variant="caption" color="textSecondary" sx={{ textAlign: 'center' }}>
            Taking longer than expected? Try clicking the button below:
          </Typography>
          <Button variant="contained" color="primary" onClick={handleManualRedirect} sx={{ minWidth: 200 }}>
            Go to Login
          </Button>
        </Box>
      )}

      {/* General fallback message */}
      {redirectAttempted && !showFallbackButton && (
        <Typography variant="caption" color="textSecondary" sx={{ mt: 2 }}>
          If you are not redirected automatically, please check your browser settings
        </Typography>
      )}
    </Box>
  );
}
