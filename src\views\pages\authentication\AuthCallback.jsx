import React, { useEffect, useState, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Box, CircularProgress, Typography, Alert } from '@mui/material';
import { useAuth } from '../../../contexts/AuthContext';
import { handleKeycloakCallback } from '../../../utils/keycloak';

// Authentication callback component for handling Keycloak redirects
const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();
  const [error, setError] = useState('');
  const [processing, setProcessing] = useState(false);
  const processedRef = useRef(false);

  // Cleanup function to clear processed code on unmount
  useEffect(() => {
    return () => {
      // Clear processed code when component unmounts to prevent issues on subsequent visits
      sessionStorage.removeItem('processed_auth_code');
    };
  }, []);

  useEffect(() => {
    const processCallback = async () => {
      // Prevent multiple executions
      if (processedRef.current || processing) {
        console.log('🔐 Callback already being processed, skipping...');
        return;
      }

      try {
        console.log('🔐 Starting callback processing...');
        processedRef.current = true;
        setProcessing(true);
        // Get authorization code from URL parameters
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        console.log('🔐 Callback URL parameters:', {
          hasCode: !!code,
          codeLength: code?.length,
          hasState: !!state,
          hasError: !!error,
          fullUrl: window.location.href
        });

        if (error) {
          throw new Error(`Authentication error: ${error}`);
        }

        if (!code) {
          throw new Error('No authorization code received');
        }

        // Check if this code has already been processed
        const processedCode = sessionStorage.getItem('processed_auth_code');
        if (processedCode === code) {
          console.log('🔐 This authorization code was already processed, redirecting to dashboard');
          // Don't throw error, just redirect to dashboard
          const returnUrl = sessionStorage.getItem('auth_redirect_url') || '/app/dashboard/default';
          sessionStorage.removeItem('auth_redirect_url');
          console.log('🔐 Redirecting to:', returnUrl);

          // Clear URL parameters to prevent issues with browser navigation
          window.history.replaceState({}, document.title, window.location.pathname);

          navigate(returnUrl, { replace: true });
          return;
        }

        // Mark this code as being processed
        sessionStorage.setItem('processed_auth_code', code);

        // Exchange code for tokens
        console.log('🔐 Processing authorization code...');
        const tokens = await handleKeycloakCallback(code);
        console.log('🔐 Received tokens:', {
          hasAccessToken: !!tokens.access_token,
          hasRefreshToken: !!tokens.refresh_token,
          hasIdToken: !!tokens.id_token
        });

        // Login with the received tokens
        console.log('🔐 Logging in with tokens...');
        const result = await login(tokens);

        console.log('🔐 Login successful:', result);

        // Redirect to intended page or dashboard
        const returnUrl = sessionStorage.getItem('auth_redirect_url') || (state ? decodeURIComponent(state) : '/app/dashboard/default');

        // Clear the stored redirect URL and processed code
        sessionStorage.removeItem('auth_redirect_url');
        sessionStorage.removeItem('processed_auth_code');

        console.log('🔐 Redirecting to:', returnUrl);

        // Clear URL parameters to prevent issues with browser navigation
        window.history.replaceState({}, document.title, window.location.pathname);

        navigate(returnUrl, { replace: true });
      } catch (error) {
        console.error('🔐 Authentication callback failed:', error);

        // Provide more specific error messages
        let errorMessage = 'Authentication failed';
        let redirectDelay = 3000;

        if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Backend server is unavailable. Using fallback authentication...';

          // Try fallback authentication after a short delay
          setTimeout(async () => {
            try {
              console.log('🔐 Attempting fallback authentication...');
              // This will trigger the fallback in authService
              const mockTokens = {
                access_token: 'fallback_token',
                refresh_token: 'fallback_refresh',
                id_token: 'fallback_id'
              };

              const result = await login(mockTokens);
              console.log('🔐 Fallback authentication successful:', result);

              const returnUrl = sessionStorage.getItem('auth_redirect_url') || '/app/dashboard/default';
              sessionStorage.removeItem('auth_redirect_url');
              navigate(returnUrl, { replace: true });
            } catch (fallbackError) {
              console.error('🔐 Fallback authentication failed:', fallbackError);
              setError('Authentication failed completely. Please try again.');
              setTimeout(() => {
                navigate('/pages/login', { replace: true });
              }, 3000);
            }
          }, 2000);
        } else if (error.message.includes('invalid_grant') || error.message.includes('Authorization code is invalid')) {
          errorMessage = 'Authorization code expired or invalid. Redirecting to login...';
          redirectDelay = 2000; // Faster redirect for auth errors

          console.log('🔐 Invalid grant error - clearing any stored auth data');
          // Clear any stored auth data that might be causing issues
          sessionStorage.removeItem('auth_redirect_url');
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('id_token');
          localStorage.removeItem('user_data');

          setError(errorMessage);
          setTimeout(() => {
            navigate('/pages/login', { replace: true });
          }, redirectDelay);
        } else if (error.message.includes('Authorization code already used')) {
          // Handle already used code gracefully - just redirect to dashboard
          console.log('🔐 Code already used, redirecting to dashboard');
          const returnUrl = sessionStorage.getItem('auth_redirect_url') || '/app/dashboard/default';
          sessionStorage.removeItem('auth_redirect_url');
          navigate(returnUrl, { replace: true });
          return;
        } else {
          setError(errorMessage);
          // Redirect to login page after a delay
          setTimeout(() => {
            navigate('/pages/login', { replace: true });
          }, redirectDelay);
        }
      } finally {
        setProcessing(false);
      }
    };

    // Only run once when component mounts
    if (!processedRef.current && !processing) {
      processCallback();
    }
  }, [searchParams, login, navigate, processing]);

  if (error) {
    return (
      <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100vh" gap={2} p={3}>
        <Alert severity="error" sx={{ maxWidth: 400 }}>
          {error}
        </Alert>
        <Typography variant="body2" color="textSecondary">
          Redirecting to login page...
        </Typography>
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100vh" gap={2}>
      <CircularProgress size={60} />
      <Typography variant="h6" color="textSecondary">
        Completing authentication...
      </Typography>
      <Typography variant="body2" color="textSecondary">
        Please wait while we verify your credentials.
      </Typography>
    </Box>
  );
};

export default AuthCallback;
