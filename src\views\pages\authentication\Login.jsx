import { useEffect } from 'react';
import { redirectToKeycloakLogin } from '../../../utils/keycloak';

// ================================|| KEYCLOAK LOGIN REDIRECT ||================================ //

export default function Login() {
  // Immediately redirect to Keycloak - no UI needed
  useEffect(() => {
    console.log('🔐 Login page accessed - redirecting to Keycloak immediately');
    redirectToKeycloakLogin();
  }, []);

  // This component should never render since we redirect immediately
  return null;
}
